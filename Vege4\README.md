# Vege4 - 現代化菜價查詢應用程式

## 📱 專案簡介

Vege4 是一個基於 Android 平台的菜價查詢應用程式，採用現代化的 Android 開發技術和架構模式。本專案是對舊版 VegePrice3 的完全重構和優化，提供更好的使用者體驗和程式碼品質。

## ✨ 主要功能

- **即時菜價查詢**：從政府開放資料平台獲取最新的農產品交易行情
- **智慧搜尋**：支援作物名稱的模糊搜尋
- **分類篩選**：按照作物種類進行篩選
- **多種排序**：支援按日期、名稱、價格等多種方式排序
- **收藏功能**：收藏感興趣的菜價資訊
- **離線瀏覽**：本地快取資料，支援離線查看
- **詳細資訊**：查看完整的價格和市場資訊

## 🏗️ 技術架構

### 架構模式
- **Clean Architecture**：清晰的分層架構
- **MVVM**：Model-View-ViewModel 設計模式
- **Repository Pattern**：統一的資料存取介面

### 技術選型
- **UI 框架**：Jetpack Compose
- **依賴注入**：Hilt
- **網路請求**：Retrofit + OkHttp
- **本地儲存**：Room Database
- **非同步處理**：Kotlin Coroutines + Flow
- **狀態管理**：StateFlow + Compose State
- **測試框架**：JUnit + MockK

### 專案結構
```
app/src/main/java/com/oojohn/vege4/
├── data/                    # 資料層
│   ├── api/                # API 服務
│   ├── database/           # 本地資料庫
│   ├── model/              # 資料模型
│   └── repository/         # Repository 實作
├── domain/                 # 領域層
│   ├── repository/         # Repository 介面
│   └── usecase/           # Use Cases
├── presentation/           # 展示層
│   ├── components/         # UI 組件
│   ├── screen/            # 畫面
│   └── viewmodel/         # ViewModel
├── di/                    # 依賴注入
├── utils/                 # 工具類別
└── ui/theme/              # UI 主題
```

## 🚀 開始使用

### 環境需求
- Android Studio Hedgehog | 2023.1.1 或更新版本
- Android SDK API 24 (Android 7.0) 或更高版本
- Kotlin 1.9.0 或更新版本

### 安裝步驟
1. 複製專案到本地
```bash
git clone [repository-url]
cd Vege4
```

2. 使用 Android Studio 開啟專案

3. 同步 Gradle 檔案

4. 執行應用程式

### 建置指令
```bash
# 建置 Debug 版本
./gradlew assembleDebug

# 執行測試
./gradlew test

# 執行 UI 測試
./gradlew connectedAndroidTest
```

## 📊 API 資料來源

本應用程式使用行政院農業委員會提供的開放資料：
- **資料來源**：農產品交易行情
- **API 端點**：https://data.coa.gov.tw/Service/OpenData/FromM/FarmTransData.aspx
- **更新頻率**：每日更新
- **資料格式**：JSON

## 🔧 主要改進

相較於舊版 VegePrice3，Vege4 具有以下改進：

### 架構優化
- ✅ 採用 Clean Architecture 和 MVVM 模式
- ✅ 使用 Hilt 進行依賴注入
- ✅ 實作 Repository 模式統一資料存取
- ✅ 使用 Use Cases 封裝業務邏輯

### UI/UX 提升
- ✅ 使用 Jetpack Compose 建構現代化 UI
- ✅ Material Design 3 設計語言
- ✅ 響應式設計和流暢動畫
- ✅ 改善的搜尋和篩選體驗

### 功能增強
- ✅ 收藏功能
- ✅ 離線瀏覽支援
- ✅ 多種排序選項
- ✅ 詳細的菜價資訊展示
- ✅ 錯誤處理和重試機制

### 效能優化
- ✅ 本地資料庫快取
- ✅ 網路請求重試機制
- ✅ 記憶體使用優化
- ✅ 載入狀態指示

## 🧪 測試

專案包含完整的測試覆蓋：

### 單元測試
- Repository 測試
- ViewModel 測試
- Use Case 測試

### 執行測試
```bash
# 執行所有單元測試
./gradlew test

# 執行特定測試類別
./gradlew test --tests VegetableRepositoryImplTest
```

## 📱 使用說明

### 主要功能操作

1. **查看菜價列表**
   - 開啟應用程式即可看到最新的菜價資訊
   - 下拉重新整理獲取最新資料

2. **搜尋菜價**
   - 在搜尋欄輸入作物名稱
   - 支援模糊搜尋，例如輸入「蘿蔔」可找到所有相關作物

3. **篩選和排序**
   - 點擊種類標籤進行篩選
   - 點擊排序按鈕選擇排序方式

4. **收藏功能**
   - 點擊愛心圖示收藏/取消收藏
   - 在收藏頁面查看所有收藏的菜價

5. **查看詳細資訊**
   - 點擊菜價卡片查看完整的價格和市場資訊

## 🤝 貢獻指南

歡迎提交 Issue 和 Pull Request 來改善這個專案！

### 開發流程
1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 📄 授權條款

本專案採用 MIT 授權條款 - 詳見 [LICENSE](LICENSE) 檔案

## 📞 聯絡資訊

如有任何問題或建議，請透過以下方式聯絡：
- 開發者：oojohn
- Email：[<EMAIL>]
- GitHub：[your-github-profile]

---

**Vege4** - 讓菜價查詢變得更簡單、更現代化！ 🥬📱
