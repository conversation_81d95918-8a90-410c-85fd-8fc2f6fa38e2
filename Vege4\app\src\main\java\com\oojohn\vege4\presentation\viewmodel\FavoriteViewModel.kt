package com.oojohn.vege4.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.domain.usecase.GetFavoriteVegetablesUseCase
import com.oojohn.vege4.domain.usecase.UpdateFavoriteStatusUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 收藏頁面的 ViewModel
 */
@HiltViewModel
class FavoriteViewModel @Inject constructor(
    private val getFavoriteVegetablesUseCase: GetFavoriteVegetablesUseCase,
    private val updateFavoriteStatusUseCase: UpdateFavoriteStatusUseCase
) : ViewModel() {
    
    // 收藏的菜價列表
    val favoriteVegetables: StateFlow<List<VegetablePrice>> = 
        getFavoriteVegetablesUseCase()
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = emptyList()
            )
    
    // 載入狀態
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    /**
     * 切換收藏狀態
     */
    fun toggleFavorite(vegetableId: String, currentFavoriteStatus: Boolean) {
        viewModelScope.launch {
            _isLoading.value = true
            
            when (val result = updateFavoriteStatusUseCase(vegetableId, !currentFavoriteStatus)) {
                is ApiResult.Success -> {
                    // 成功，資料會透過 Flow 自動更新
                }
                is ApiResult.Error -> {
                    // 處理錯誤，可以顯示 Toast 或 Snackbar
                }
                is ApiResult.Loading -> {
                    // 不需要特別處理
                }
            }
            
            _isLoading.value = false
        }
    }
}
