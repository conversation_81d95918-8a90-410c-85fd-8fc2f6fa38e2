package com.oojohn.vege4.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CloudOff
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.WifiOff
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.oojohn.vege4.ui.theme.Vege4Theme

/**
 * 錯誤狀態組件
 */
@Composable
fun ErrorState(
    error: Throwable,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val (icon, title, message) = when (error) {
        is java.net.UnknownHostException,
        is java.net.ConnectException -> Triple(
            Icons.Default.WifiOff,
            "網路連線錯誤",
            "請檢查您的網路連線並重試"
        )
        is java.net.SocketTimeoutException -> Triple(
            Icons.Default.CloudOff,
            "連線逾時",
            "伺服器回應時間過長，請稍後再試"
        )
        else -> Triple(
            Icons.Default.Error,
            "載入失敗",
            error.message ?: "發生未知錯誤，請重試"
        )
    }
    
    ErrorStateContent(
        icon = icon,
        title = title,
        message = message,
        onRetryClick = onRetryClick,
        modifier = modifier
    )
}

/**
 * 錯誤狀態內容組件
 */
@Composable
fun ErrorStateContent(
    icon: ImageVector,
    title: String,
    message: String,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onRetryClick
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("重試")
        }
    }
}

/**
 * 網路錯誤 Snackbar
 */
@Composable
fun NetworkErrorSnackbar(
    snackbarHostState: SnackbarHostState,
    isConnected: Boolean
) {
    if (!isConnected) {
        LaunchedEffect(isConnected) {
            snackbarHostState.showSnackbar(
                message = "網路連線中斷",
                duration = SnackbarDuration.Indefinite
            )
        }
    }
}

/**
 * 載入中指示器（帶文字）
 */
@Composable
fun LoadingIndicatorWithText(
    text: String = "載入中...",
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator()
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 重新整理指示器
 */
@Composable
fun RefreshIndicator(
    isRefreshing: Boolean,
    modifier: Modifier = Modifier
) {
    if (isRefreshing) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "更新中...",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 快取狀態指示器
 */
@Composable
fun CacheStatusIndicator(
    lastUpdateTime: Long?,
    modifier: Modifier = Modifier
) {
    if (lastUpdateTime != null) {
        val cacheTime = com.oojohn.vege4.utils.CacheUtils.formatCacheTime(lastUpdateTime)
        
        Surface(
            modifier = modifier,
            color = MaterialTheme.colorScheme.surfaceVariant,
            shape = MaterialTheme.shapes.small
        ) {
            Text(
                text = cacheTime,
                style = MaterialTheme.typography.labelSmall,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ErrorStatePreview() {
    Vege4Theme {
        ErrorStateContent(
            icon = Icons.Default.WifiOff,
            title = "網路連線錯誤",
            message = "請檢查您的網路連線並重試",
            onRetryClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun LoadingIndicatorPreview() {
    Vege4Theme {
        LoadingIndicatorWithText("載入菜價資料中...")
    }
}
