<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":app" external.linked.project.path="$MODULE_DIR$/../../../app" external.root.project.path="$MODULE_DIR$/../../.." external.system.id="GRADLE" external.system.module.group="VegePrice3" external.system.module.version="unspecified" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":app" />
        <option name="LAST_SUCCESSFUL_SYNC_AGP_VERSION" value="7.0.2" />
        <option name="LAST_KNOWN_AGP_VERSION" value="7.0.2" />
      </configuration>
    </facet>
    <facet type="kotlin-language" name="Kotlin">
      <configuration version="4" platform="JVM 1.8" allPlatforms="JVM [1.8]" useProjectSettings="false">
        <compilerSettings>
          <option name="additionalArguments" value="-Xallow-no-source-files" />
        </compilerSettings>
        <compilerArguments>
          <option name="destination" value="$MODULE_DIR$/../../../app/build/tmp/kotlin-classes/debug" />
          <option name="classpath" value="$MODULE_DIR$/../../../app/build/intermediates/compile_and_runtime_not_namespaced_r_class_jar/debug/R.jar;C:/Users/<USER>/.gradle/caches/transforms-3/dcbb200a18f2866fa79443a290a6ced1/transformed/jetified-kotlin-stdlib-jdk8-1.5.30.jar;C:/Users/<USER>/.gradle/caches/transforms-3/6c4fc95dd796ed0a87be8b26b66c39c0/transformed/jetified-kotlin-android-extensions-runtime-1.5.30.jar;C:/Users/<USER>/.gradle/caches/transforms-3/d2150c754d06d368e4432c4df8e8f53e/transformed/jetified-core-ktx-1.6.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/0b8cedc1e71d8ed9f49bcadf0caa648c/transformed/jetified-anko-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/ee2082227800cfe72d953bc212269bec/transformed/jetified-kotlin-stdlib-jdk7-1.5.30.jar;C:/Users/<USER>/.gradle/caches/transforms-3/91f7cb1b15650d61c8eeaae8aedbc417/transformed/jetified-anko-sqlite-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/47b0aea8cca1dee8519f736b38d4cb89/transformed/jetified-anko-sdk27-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/80cbafba03e5d554e7a82bf531dbde26/transformed/jetified-anko-sdk27-coroutines-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/b9146b4a457b78a2de2e90810a20a85a/transformed/jetified-anko-appcompat-v7-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/5d6bbfa548b99b560d913e225120286a/transformed/jetified-anko-support-v4-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/5c9fb0a3e523e6a3682a10dd835579fa/transformed/jetified-anko-appcompat-v7-coroutines-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/ad20afa6511e3b677c8c77336079ef3f/transformed/jetified-anko-appcompat-v7-commons-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/d84404a17a00b70ae34215233ae07d7e/transformed/jetified-anko-support-v4-commons-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/3bc5d54aa2d6a98848d5cb9408f76f34/transformed/jetified-anko-commons-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/ebe1bdc359ecc0dafd8978d4be912e28/transformed/jetified-anko-coroutines-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/0a470269bd728be22d7f0ab00a9d5b4e/transformed/jetified-sqlite-base-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/bf3807081794f55c641ed6d27ae18a07/transformed/jetified-platform-base-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/e6d9517468436e5e04c5d65546e3afbb/transformed/jetified-appcompatV7-base-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/7d660503e667101cd3547d9c306a5e2e/transformed/jetified-supportV4-base-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/1265c6e222779a5638b94d439b85be97/transformed/jetified-commons-base-0.10.8-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/4c6aa0a5bbbd9fabd07cce20bce2421f/transformed/jetified-kotlinx-coroutines-android-1.0.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/28ec8060a265bb2cd14048bec730b80d/transformed/jetified-kotlinx-coroutines-core-1.0.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/047d70b3e9cdf8ce35561d06376b2ce0/transformed/jetified-kotlin-stdlib-1.5.30.jar;C:/Users/<USER>/.gradle/caches/transforms-3/8944b87225715a8d682ce21a3dffc5b9/transformed/jetified-annotations-13.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/f86676091a3e7538b6ad25e14bb744ab/transformed/jetified-kotlinx-coroutines-core-common-1.0.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/af10ceb8c543470d0c20f53bbcf9e796/transformed/jetified-kotlin-stdlib-common-1.5.30.jar;C:/Users/<USER>/.gradle/caches/transforms-3/eac15544005595a0d936583ba54be438/transformed/material-1.4.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/7171dd648094d65f4a1a44364a287d63/transformed/appcompat-1.3.1-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/f003c518eba0162c881232bcfc7a517e/transformed/jetified-viewpager2-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/3476e6ac9e15fd723d60484142335aec/transformed/legacy-support-v4-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/4586a8a8d727d063ec5007cec79b2e92/transformed/fragment-1.3.6-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/cb3d3a6a471474bf0d302c965210fe6e/transformed/jetified-activity-1.2.4-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/3ee1612644ee8e478c2419beda7bef84/transformed/jetified-appcompat-resources-1.3.1-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/4ea0e3a987b34d28adab195297e0e031/transformed/legacy-support-core-ui-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/b459c25cc4d0466335c0226c54357a4d/transformed/drawerlayout-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/e1700653095aa9ead5a1c023df9bedf7/transformed/coordinatorlayout-1.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/6987e45a18c2ccd12b22a09ce5cbea42/transformed/dynamicanimation-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/5d911f9ca7d145c090e9ebae9de62a1a/transformed/recyclerview-1.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/613c3f95fa69923b2628f51cfec16371/transformed/transition-1.2.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/c6205a90dcf4561c00b8b2d7e834d151/transformed/vectordrawable-animated-1.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/06435ad72147bbbc0cd32a660fc82051/transformed/vectordrawable-1.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/a159244409a81c965f212b42bcbf02ba/transformed/viewpager-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/9c74a5844ef76646bf99181053ee5e3e/transformed/legacy-support-core-utils-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/67d9f62ad6542ddb21d0ded79bc8cf9c/transformed/loader-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/5c15efac73696bb4e7030befdc1bcd16/transformed/slidingpanelayout-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/b70a25fa6510415ff13605f31c345d1c/transformed/customview-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/cf493da05891193f53bfe8c783900fbb/transformed/media-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/f7b9c4cf95bd0a28a47527e41342733a/transformed/swiperefreshlayout-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/2e18f0edfcabf9fd52cdc56a0e8d8d96/transformed/asynclayoutinflater-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/c1d8d72a59f68aeefc7e7c875014090a/transformed/core-1.6.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/9436bfdb1761259377d8101fa72be7ff/transformed/cursoradapter-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/1ee1125c2bc922810840cfad9f3c29f5/transformed/jetified-lifecycle-viewmodel-savedstate-2.3.1-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/c610ea115c40abec397f103ee300b2fb/transformed/jetified-savedstate-1.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/d8662dcd431c77102579459385029ce7/transformed/cardview-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/ec5b0e049966f4a6766b54bdeb2576d5/transformed/lifecycle-runtime-2.3.1-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/c8d4c62a996464b45926ca15cfd1417c/transformed/versionedparcelable-1.1.1-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/aeec57da4d1337e140c819513de89c20/transformed/lifecycle-viewmodel-2.3.1-api.jar;C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/659df186b2c573798cab13853ca9c204/transformed/lifecycle-livedata-2.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/2a6b34a8ddd7263507443a0a61137b70/transformed/lifecycle-livedata-core-2.3.1-api.jar;C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.3.1/fc466261d52f4433863642fb40d12441ae274a98/lifecycle-common-2.3.1.jar;C:/Users/<USER>/.gradle/caches/transforms-3/d64b88e1b7f8e10b2815f668755a0603/transformed/core-runtime-2.1.0-api.jar;C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.1.0/b3152fc64428c9354344bd89848ecddc09b6f07e/core-common-2.1.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/36402bb4d448d4bea6ca26604730ac6a/transformed/interpolator-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/167280c4a6c39a9634e4696be801217e/transformed/documentfile-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/e8787e9cfa247b5169003b68d028f2f3/transformed/localbroadcastmanager-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/08e6841d0e0a6eca3f77a087907a1295/transformed/print-1.0.0-api.jar;C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation/1.2.0/57136ff68ee784c6e19db34ed4a175338fadfde1/annotation-1.2.0.jar;C:/Users/<USER>/.gradle/caches/transforms-3/b51f137faaf18a14f49cdb8c32c6fe8b/transformed/jetified-annotation-experimental-1.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/0405767d4157f1ae111cc5eeb654f75e/transformed/constraintlayout-2.1.0-api.jar;C:/Users/<USER>/.gradle/caches/transforms-3/acbe24a65e3618ec10cac207c72b8fd6/transformed/jetified-gson-2.8.8.jar;C:/Users/<USER>/AppData/Local/Android/Sdk/platforms/android-30/android.jar;C:/Users/<USER>/AppData/Local/Android/Sdk/build-tools/30.0.2/core-lambda-stubs.jar" />
          <option name="noStdlib" value="true" />
          <option name="noReflect" value="true" />
          <option name="moduleName" value="app_debug" />
          <option name="languageVersion" value="1.5" />
          <option name="apiVersion" value="1.5" />
          <option name="pluginOptions">
            <array>
              <option value="plugin:org.jetbrains.kotlin.android:experimental=false" />
              <option value="plugin:org.jetbrains.kotlin.android:enabled=true" />
              <option value="plugin:org.jetbrains.kotlin.android:defaultCacheImplementation=hashMap" />
            </array>
          </option>
          <option name="pluginClasspaths">
            <array>
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-android-extensions/1.5.30/81d12fd9082cfcda1d1c5c5df3c1c636e5dcc4f/kotlin-android-extensions-1.5.30.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-compiler-embeddable/1.5.30/3362a354066df1578a057c200da23cf2a1d8144f/kotlin-compiler-embeddable-1.5.30.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-reflect/1.5.30/6773e974437dd6432aa646cc2f8ab71de42b5773/kotlin-reflect-1.5.30.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.5.30/d68efdea04955974ac1020f8f66ef8176bfbce1f/kotlin-stdlib-1.5.30.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-script-runtime/1.5.30/10697ab20bfd2f1b23baa91306950c36c4e89a8d/kotlin-script-runtime-1.5.30.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-daemon-embeddable/1.5.30/e355dc9011e559a475608265607948109e113ae0/kotlin-daemon-embeddable-1.5.30.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.intellij.deps/trove4j/1.0.20181211/216c2e14b070f334479d800987affe4054cd563f/trove4j-1.0.20181211.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/13.0/919f0dfe192fb4e063e7dacadee7f8bb9a2672a9/annotations-13.0.jar" />
              <option value="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.5.30/649ffab7767038323fec0cc41e2d7b0a8f65a378/kotlin-stdlib-common-1.5.30.jar" />
            </array>
          </option>
          <option name="errors">
            <ArgumentParseErrors />
          </option>
        </compilerArguments>
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDebugSources" />
        <afterSyncTasks>
          <task>generateDebugSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../app/src/main/res;file://$MODULE_DIR$/../../../app/src/debug/res;file://$MODULE_DIR$/../../../app/build/generated/res/rs/debug;file://$MODULE_DIR$/../../../app/build/generated/res/resValues/debug" />
        <option name="TEST_RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../app/src/androidTest/res;file://$MODULE_DIR$/../../../app/src/androidTestDebug/res;file://$MODULE_DIR$/../../../app/build/generated/res/rs/androidTest/debug;file://$MODULE_DIR$/../../../app/build/generated/res/resValues/androidTest/debug" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/../../../app/build/intermediates/javac/debug/classes" />
    <output-test url="file://$MODULE_DIR$/../../../app/build/intermediates/javac/debugUnitTest/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../../app">
      <sourceFolder url="file://$MODULE_DIR$/../../../app/build/generated/ap_generated_sources/debug/out" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/../../../app/build/generated/res/resValues/debug" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/../../../app/build/generated/source/buildConfig/debug" isTestSource="false" generated="true" />
      <sourceFolder url="file://$MODULE_DIR$/../../../app/src/androidTest/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/../../../app/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/../../../app/src/main/res" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/../../../app/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/../../../app/.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/../../../app/build" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 30 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="TEST" name="Gradle: junit:junit:4.13.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: org.hamcrest:hamcrest-integration:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: org.hamcrest:hamcrest-library:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: com.google.code.findbugs:jsr305:2.0.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: com.squareup:javawriter:2.1.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: javax.inject:javax.inject:1" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test.ext:junit:1.1.3@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test.espresso:espresso-core:3.4.0@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test:runner:1.4.0@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test:core:1.4.0@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test.services:storage:1.4.0@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test:monitor:1.4.0@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.arch.core:core-runtime:2.0.0@aar" level="project" />
    <orderEntry type="library" scope="TEST" name="Gradle: androidx.test.espresso:espresso-idling-resource:3.4.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.5.30" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.5.30" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.5.30" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlinx:kotlinx-coroutines-android:1.0.0" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlinx:kotlinx-coroutines-core:1.0.0" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib:1.5.30" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains:annotations:13.0" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlinx:kotlinx-coroutines-core-common:1.0.0" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-common:1.5.30" level="project" />
    <orderEntry type="library" name="Gradle: androidx.collection:collection:1.1.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-common:2.3.1" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-common:2.1.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation:1.2.0" level="project" />
    <orderEntry type="library" name="Gradle: com.google.code.gson:gson:2.8.8" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core-ktx:1.6.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-sqlite:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-sdk27:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-sdk27-coroutines:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-appcompat-v7:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-support-v4:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-appcompat-v7-coroutines:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-appcompat-v7-commons:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-support-v4-commons:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-commons:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:anko-coroutines:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:sqlite-base:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:platform-base:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:appcompatV7-base:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:supportV4-base:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: org.jetbrains.anko:commons-base:0.10.8@aar" level="project" />
    <orderEntry type="library" name="Gradle: com.google.android.material:material:1.4.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat:1.3.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager2:viewpager2:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.legacy:legacy-support-v4:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.fragment:fragment:1.3.6@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.activity:activity:1.2.4@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat-resources:1.3.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.legacy:legacy-support-core-ui:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.drawerlayout:drawerlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.dynamicanimation:dynamicanimation:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.recyclerview:recyclerview:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.transition:transition:1.2.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager:viewpager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.legacy:legacy-support-core-utils:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.loader:loader:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.customview:customview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.media:media:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.swiperefreshlayout:swiperefreshlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core:1.6.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cursoradapter:cursoradapter:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.savedstate:savedstate:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cardview:cardview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-runtime:2.3.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.versionedparcelable:versionedparcelable:1.1.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.3.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata:2.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.3.1@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-runtime:2.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.interpolator:interpolator:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.documentfile:documentfile:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.print:print:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation-experimental:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.constraintlayout:constraintlayout:2.1.0@aar" level="project" />
  </component>
</module>