package com.oojohn.vege4.data.api

import com.oojohn.vege4.data.model.VegetablePrice
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * 菜價 API 服務介面
 */
interface VegetableApiService {
    
    /**
     * 取得菜價資料
     * 使用政府開放資料平台的農產品交易行情 API
     */
    @GET("Service/OpenData/FromM/FarmTransData.aspx")
    suspend fun getVegetablePrices(): Response<List<VegetablePrice>>
    
    /**
     * 根據日期範圍取得菜價資料
     */
    @GET("Service/OpenData/FromM/FarmTransData.aspx")
    suspend fun getVegetablePricesByDateRange(
        @Query("StartDate") startDate: String,
        @Query("EndDate") endDate: String
    ): Response<List<VegetablePrice>>
    
    /**
     * 根據作物名稱搜尋
     */
    @GET("Service/OpenData/FromM/FarmTransData.aspx")
    suspend fun searchVegetablesByName(
        @Query("CropName") cropName: String
    ): Response<List<VegetablePrice>>
    
    companion object {
        const val BASE_URL = "https://data.coa.gov.tw/"
        
        // API 相關常數
        const val TIMEOUT_SECONDS = 30L
        const val CACHE_SIZE = 10 * 1024 * 1024L // 10MB
        
        // 錯誤訊息
        const val ERROR_NETWORK = "網路連線錯誤，請檢查網路設定"
        const val ERROR_SERVER = "伺服器錯誤，請稍後再試"
        const val ERROR_TIMEOUT = "連線逾時，請稍後再試"
        const val ERROR_UNKNOWN = "發生未知錯誤"
        const val ERROR_NO_DATA = "查無資料"
    }
}

/**
 * API 結果封裝類別
 */
sealed class ApiResult<out T> {
    data class Success<out T>(val data: T) : ApiResult<T>()
    data class Error(val exception: Throwable) : ApiResult<Nothing>()
    data object Loading : ApiResult<Nothing>()
}

/**
 * 網路錯誤類型
 */
sealed class NetworkError : Exception() {
    data object NetworkUnavailable : NetworkError()
    data object Timeout : NetworkError()
    data class ServerError(val code: Int, override val message: String) : NetworkError()
    data class UnknownError(override val message: String) : NetworkError()
}
