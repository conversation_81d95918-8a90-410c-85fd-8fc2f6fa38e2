package com.oojohn.vege4.domain.usecase

import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.model.SortOption
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.domain.repository.VegetableRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 取得菜價資料的 Use Case
 */
class GetVegetablesUseCase @Inject constructor(
    private val repository: VegetableRepository
) {
    
    /**
     * 取得所有菜價資料並套用篩選和排序
     */
    operator fun invoke(
        searchQuery: String = "",
        categoryFilter: String = "全部",
        sortOption: SortOption = SortOption.DATE_DESC
    ): Flow<List<VegetablePrice>> {
        return when {
            searchQuery.isNotBlank() -> repository.searchVegetablesByName(searchQuery)
            categoryFilter != "全部" -> repository.getVegetablesByCategory(categoryFilter)
            else -> repository.getAllVegetables()
        }.map { vegetables ->
            sortVegetables(vegetables, sortOption)
        }
    }
    
    /**
     * 根據排序選項排序菜價資料
     */
    private fun sortVegetables(
        vegetables: List<VegetablePrice>,
        sortOption: SortOption
    ): List<VegetablePrice> {
        return when (sortOption) {
            SortOption.DATE_DESC -> vegetables.sortedByDescending { it.交易日期 }
            SortOption.DATE_ASC -> vegetables.sortedBy { it.交易日期 }
            SortOption.NAME_ASC -> vegetables.sortedBy { it.作物名稱 }
            SortOption.NAME_DESC -> vegetables.sortedByDescending { it.作物名稱 }
            SortOption.PRICE_ASC -> vegetables.sortedBy { parsePrice(it.平均價) }
            SortOption.PRICE_DESC -> vegetables.sortedByDescending { parsePrice(it.平均價) }
        }
    }
    
    /**
     * 解析價格字串為數字，用於排序
     */
    private fun parsePrice(priceString: String?): Double {
        return try {
            priceString?.replace(",", "")?.toDoubleOrNull() ?: 0.0
        } catch (e: Exception) {
            0.0
        }
    }
}

/**
 * 重新整理菜價資料的 Use Case
 */
class RefreshVegetablesUseCase @Inject constructor(
    private val repository: VegetableRepository
) {
    suspend operator fun invoke(): ApiResult<List<VegetablePrice>> {
        return repository.refreshVegetables()
    }
}

/**
 * 取得收藏菜價的 Use Case
 */
class GetFavoriteVegetablesUseCase @Inject constructor(
    private val repository: VegetableRepository
) {
    operator fun invoke(): Flow<List<VegetablePrice>> {
        return repository.getFavoriteVegetables()
    }
}

/**
 * 更新收藏狀態的 Use Case
 */
class UpdateFavoriteStatusUseCase @Inject constructor(
    private val repository: VegetableRepository
) {
    suspend operator fun invoke(id: String, isFavorite: Boolean): ApiResult<Unit> {
        return repository.updateFavoriteStatus(id, isFavorite)
    }
}

/**
 * 取得所有種類的 Use Case
 */
class GetCategoriesUseCase @Inject constructor(
    private val repository: VegetableRepository
) {
    suspend operator fun invoke(): ApiResult<List<String>> {
        return repository.getAllCategories()
    }
}
