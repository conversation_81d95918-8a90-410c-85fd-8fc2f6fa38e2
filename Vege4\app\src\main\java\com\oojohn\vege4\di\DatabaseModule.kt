package com.oojohn.vege4.di

import android.content.Context
import androidx.room.Room
import com.oojohn.vege4.data.database.VegetableDao
import com.oojohn.vege4.data.database.VegetableDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 資料庫依賴注入模組
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideVegetableDatabase(
        @ApplicationContext context: Context
    ): VegetableDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            VegetableDatabase::class.java,
            "vegetable_database"
        )
            .fallbackToDestructiveMigration()
            .build()
    }
    
    @Provides
    fun provideVegetableDao(database: VegetableDatabase): VegetableDao {
        return database.vegetableDao()
    }
}
