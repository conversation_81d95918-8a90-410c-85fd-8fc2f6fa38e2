package com.oojohn.vegeprice3

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.android.synthetic.main.activity_rview.*
import kotlinx.android.synthetic.main.row_vegeprice.view.*
import org.jetbrains.anko.*
import java.net.URL


class RView : AppCompatActivity() ,AnkoLogger {
    var vegelots:List<VegeLot>?=null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_rview)


        val vege = "https://data.coa.gov.tw/Service/OpenData/FromM/FarmTransData.aspx"

        doAsync {
            val url = URL(vege)
            val json: String = url.readText()
            info(json)

            vegelots=Gson().fromJson<List<VegeLot>>(json,object :TypeToken<List<VegeLot>>(){}.type)

            uiThread {

                recycle.layoutManager=LinearLayoutManager(this@RView)
                recycle.setHasFixedSize(true)
                recycle.adapter=VegeAdapter()




                //             Toast.makeText(it,"Got it",Toast.LENGTH_LONG).show()
//                toast("Got it")
//                info.text = json
//                alert("Got it", "Alert") {
//                    okButton {
//                        parseGson(json)
//                    }
//                }.show()




            }
        }


    }


    private fun parseGson(json: String) {
        val list = Gson().fromJson(json, Array<VegeLot>::class.java)
        info(list.size)
        list.forEach {
            info("${it.交易日期} ${it.種類代碼} ${it.作物名稱} ${it.作物代號}")
        }
    }


    inner class VegeAdapter(): RecyclerView.Adapter<VegeHolder>(){
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VegeHolder {
            val view =LayoutInflater.from(parent.context).inflate(R.layout.row_vegeprice,parent,false)
            return VegeHolder(view)

        }

        override fun onBindViewHolder(holder: VegeHolder, position: Int) {
            val vegelot: VegeLot?=vegelots?.get(position)
            holder.bindVege(vegelot!!)


        }

        override fun getItemCount(): Int {
            val size:Int=vegelots?.size?:0
            return size

        }


    }

    inner class VegeHolder(view: View) : RecyclerView.ViewHolder(view){
        val date :TextView=view.vegedate
        val name :TextView=view.name
        val typecode :TextView=view.typecode
        val code :TextView=view.code

        fun  bindVege(vegeLot: VegeLot){

            date.text=vegeLot.交易日期
            name.text=vegeLot.作物名稱
            typecode.text=vegeLot.種類代碼
            code.text=vegeLot.作物代號
        }


    }


}


    data class VegeLot(
        val 交易日期 :String,
        val 種類代碼 :String,
        val 作物代號 :String,
        val 作物名稱 :String)



