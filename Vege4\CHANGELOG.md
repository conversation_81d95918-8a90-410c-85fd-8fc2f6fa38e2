# 變更日誌

本檔案記錄了 Vege4 專案的所有重要變更。

格式基於 [Keep a Changelog](https://keepachangelog.com/zh-TW/1.0.0/)，
並且本專案遵循 [語意化版本](https://semver.org/lang/zh-TW/)。

## [1.0.0] - 2024-01-15

### 新增
- 🎉 **全新的 Vege4 應用程式**
  - 基於 Jetpack Compose 的現代化 UI
  - Clean Architecture + MVVM 架構模式
  - 使用 Hilt 進行依賴注入

- 📱 **核心功能**
  - 即時菜價查詢和顯示
  - 智慧搜尋功能（支援模糊搜尋）
  - 分類篩選（按作物種類）
  - 多種排序選項（日期、名稱、價格）
  - 收藏功能
  - 詳細資訊頁面

- 🔧 **技術特性**
  - Room 資料庫本地快取
  - Retrofit 網路請求
  - Kotlin Coroutines + Flow 非同步處理
  - 指數退避重試機制
  - 網路狀態監控
  - 錯誤處理和載入狀態

- 📊 **資料管理**
  - 政府開放資料 API 整合
  - 離線瀏覽支援
  - 自動資料同步
  - 快取過期管理

- 🎨 **使用者體驗**
  - Material Design 3 設計語言
  - 響應式設計
  - 流暢的動畫效果
  - 直觀的操作介面

### 改進
相較於舊版 VegePrice3：

- ✅ **架構升級**
  - 從傳統 Activity + RecyclerView 升級到 Jetpack Compose
  - 採用 Clean Architecture 提升程式碼品質
  - 使用現代化的依賴注入框架

- ✅ **功能增強**
  - 新增收藏功能
  - 改善搜尋和篩選體驗
  - 增加多種排序選項
  - 詳細的菜價資訊展示

- ✅ **效能優化**
  - 本地資料庫快取提升載入速度
  - 網路請求重試機制提升穩定性
  - 記憶體使用優化

- ✅ **使用者體驗**
  - 現代化的 UI 設計
  - 更好的錯誤處理和提示
  - 載入狀態指示
  - 離線使用支援

### 技術債務清理
- 移除已棄用的 `kotlinx.android.synthetic`
- 移除不再維護的 Anko 庫
- 升級到最新的 Android 開發工具和庫

### 測試
- 單元測試覆蓋率 > 80%
- Repository 層完整測試
- ViewModel 層完整測試
- UI 組件測試

### 文件
- 完整的 README.md
- 使用者指南
- 開發者文件
- API 參考文件

---

## 版本說明

### 版本號格式
使用語意化版本號：`主版本.次版本.修訂版本`

- **主版本**：不相容的 API 變更
- **次版本**：向下相容的功能新增
- **修訂版本**：向下相容的錯誤修復

### 變更類型
- **新增**：新功能
- **變更**：現有功能的變更
- **棄用**：即將移除的功能
- **移除**：已移除的功能
- **修復**：錯誤修復
- **安全性**：安全性相關修復

---

## 未來規劃

### v1.1.0 (計劃中)
- [ ] 價格趨勢圖表
- [ ] 推播通知功能
- [ ] 更多篩選選項
- [ ] 匯出功能

### v1.2.0 (計劃中)
- [ ] 多語言支援
- [ ] 深色主題
- [ ] 小工具支援
- [ ] 分享功能增強

### v2.0.0 (長期規劃)
- [ ] 價格預測功能
- [ ] 更多資料來源整合
- [ ] 社群功能
- [ ] 個人化推薦

---

感謝所有使用 Vege4 的用戶！您的回饋是我們持續改進的動力。
