package com.oojohn.vege4.data.repository

import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.api.NetworkError
import com.oojohn.vege4.data.api.VegetableApiService
import com.oojohn.vege4.data.database.VegetableDao
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.domain.repository.VegetableRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 菜價資料倉庫實作
 * 負責協調本地資料庫和遠端 API 的資料存取
 */
@Singleton
class VegetableRepositoryImpl @Inject constructor(
    private val apiService: VegetableApiService,
    private val dao: VegetableDao
) : VegetableRepository {
    
    override fun getAllVegetables(): Flow<List<VegetablePrice>> {
        return dao.getAllVegetables()
    }
    
    override suspend fun refreshVegetables(): ApiResult<List<VegetablePrice>> {
        return try {
            // 使用重試機制
            com.oojohn.vege4.utils.RetryUtils.retryWithExponentialBackoff(
                maxRetries = 3,
                initialDelayMs = 1000
            ) {
                val response = apiService.getVegetablePrices()
                if (response.isSuccessful) {
                    val vegetables = response.body() ?: emptyList()

                    // 更新本地資料庫
                    dao.deleteAllVegetables()
                    dao.insertVegetables(vegetables.map { vegetable ->
                        vegetable.copy(lastUpdated = System.currentTimeMillis())
                    })

                    ApiResult.Success(vegetables)
                } else {
                    throw NetworkError.ServerError(
                        response.code(),
                        response.message()
                    )
                }
            }
        } catch (e: Exception) {
            ApiResult.Error(handleNetworkError(e))
        }
    }
    
    override fun searchVegetablesByName(name: String): Flow<List<VegetablePrice>> {
        return if (name.isBlank()) {
            dao.getAllVegetables()
        } else {
            dao.searchVegetablesByName(name)
        }
    }
    
    override fun getVegetablesByCategory(categoryCode: String): Flow<List<VegetablePrice>> {
        return if (categoryCode == "全部") {
            dao.getAllVegetables()
        } else {
            dao.getVegetablesByCategory(categoryCode)
        }
    }
    
    override fun getFavoriteVegetables(): Flow<List<VegetablePrice>> {
        return dao.getFavoriteVegetables()
    }
    
    override suspend fun updateFavoriteStatus(id: String, isFavorite: Boolean): ApiResult<Unit> {
        return try {
            dao.updateFavoriteStatus(id, isFavorite)
            ApiResult.Success(Unit)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    override suspend fun getAllCategories(): ApiResult<List<String>> {
        return try {
            val categories = dao.getAllCategories()
            val allCategories = listOf("全部") + categories
            ApiResult.Success(allCategories)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    override suspend fun clearCache(): ApiResult<Unit> {
        return try {
            dao.deleteAllVegetables()
            ApiResult.Success(Unit)
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }
    
    override suspend fun hasLocalData(): Boolean {
        return try {
            dao.hasData()
        } catch (e: Exception) {
            false
        }
    }
    
    override suspend fun getLastUpdateTime(): Long? {
        return try {
            dao.getLastUpdateTime()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 處理網路錯誤
     */
    private fun handleNetworkError(exception: Exception): NetworkError {
        return when (exception) {
            is SocketTimeoutException -> NetworkError.Timeout
            is IOException -> NetworkError.NetworkUnavailable
            is HttpException -> NetworkError.ServerError(
                exception.code(),
                exception.message()
            )
            else -> NetworkError.UnknownError(
                exception.message ?: "Unknown error occurred"
            )
        }
    }
}
