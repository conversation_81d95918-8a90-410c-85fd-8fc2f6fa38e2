package com.oojohn.vege4.presentation.components

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.ui.theme.Vege4Theme
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * VegetableCard 組件的 UI 測試
 */
@RunWith(AndroidJUnit4::class)
class VegetableCardTest {
    
    @get:Rule
    val composeTestRule = createComposeRule()
    
    private val sampleVegetable = VegetablePrice(
        交易日期 = "2024/01/15",
        種類代碼 = "N04",
        作物代號 = "FN01",
        作物名稱 = "白蘿蔔",
        市場名稱 = "台北果菜市場",
        平均價 = "20",
        交易量 = "1500",
        isFavorite = false
    )
    
    @Test
    fun vegetableCard_displaysCorrectInformation() {
        // Given
        composeTestRule.setContent {
            Vege4Theme {
                VegetableCard(
                    vegetable = sampleVegetable,
                    onFavoriteClick = { _, _ -> },
                    onCardClick = { }
                )
            }
        }
        
        // Then
        composeTestRule.onNodeWithText("白蘿蔔").assertIsDisplayed()
        composeTestRule.onNodeWithText("平均價: 20 元").assertIsDisplayed()
        composeTestRule.onNodeWithText("交易量: 1500").assertIsDisplayed()
        composeTestRule.onNodeWithText("N04").assertIsDisplayed()
        composeTestRule.onNodeWithText("2024年01月15日").assertIsDisplayed()
    }
    
    @Test
    fun vegetableCard_favoriteButton_clickable() {
        // Given
        var favoriteClicked = false
        var clickedId = ""
        var clickedStatus = false
        
        composeTestRule.setContent {
            Vege4Theme {
                VegetableCard(
                    vegetable = sampleVegetable,
                    onFavoriteClick = { id, status ->
                        favoriteClicked = true
                        clickedId = id
                        clickedStatus = status
                    },
                    onCardClick = { }
                )
            }
        }
        
        // When
        composeTestRule.onNodeWithContentDescription("加入收藏").performClick()
        
        // Then
        assert(favoriteClicked)
        assert(clickedId == sampleVegetable.id)
        assert(clickedStatus == sampleVegetable.isFavorite)
    }
    
    @Test
    fun vegetableCard_cardClick_triggersCallback() {
        // Given
        var cardClicked = false
        var clickedVegetable: VegetablePrice? = null
        
        composeTestRule.setContent {
            Vege4Theme {
                VegetableCard(
                    vegetable = sampleVegetable,
                    onFavoriteClick = { _, _ -> },
                    onCardClick = { vegetable ->
                        cardClicked = true
                        clickedVegetable = vegetable
                    }
                )
            }
        }
        
        // When
        composeTestRule.onNodeWithText("白蘿蔔").performClick()
        
        // Then
        assert(cardClicked)
        assert(clickedVegetable == sampleVegetable)
    }
    
    @Test
    fun vegetableCard_favoriteState_showsCorrectIcon() {
        // Given - 收藏狀態的蔬菜
        val favoriteVegetable = sampleVegetable.copy(isFavorite = true)
        
        composeTestRule.setContent {
            Vege4Theme {
                VegetableCard(
                    vegetable = favoriteVegetable,
                    onFavoriteClick = { _, _ -> },
                    onCardClick = { }
                )
            }
        }
        
        // Then
        composeTestRule.onNodeWithContentDescription("取消收藏").assertIsDisplayed()
    }
}
