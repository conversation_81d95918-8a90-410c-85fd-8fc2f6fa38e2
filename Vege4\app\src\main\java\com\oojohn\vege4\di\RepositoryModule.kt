package com.oojohn.vege4.di

import com.oojohn.vege4.data.repository.VegetableRepositoryImpl
import com.oojohn.vege4.domain.repository.VegetableRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Repository 依賴注入模組
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    
    @Binds
    @Singleton
    abstract fun bindVegetableRepository(
        vegetableRepositoryImpl: VegetableRepositoryImpl
    ): VegetableRepository
}
