# Vege4 開發文件

## 🏗️ 架構概述

Vege4 採用 Clean Architecture 和 MVVM 模式，確保程式碼的可維護性和可測試性。

### 分層架構

```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│  (UI, ViewModels, Compose Screens)  │
├─────────────────────────────────────┤
│            Domain Layer             │
│     (Use Cases, Repository Interface) │
├─────────────────────────────────────┤
│             Data Layer              │
│  (Repository Impl, API, Database)   │
└─────────────────────────────────────┘
```

### 主要組件

#### 1. Presentation Layer
- **Compose UI**：使用 Jetpack Compose 建構的現代化 UI
- **ViewModels**：管理 UI 狀態和業務邏輯
- **Navigation**：處理畫面間的導航

#### 2. Domain Layer
- **Use Cases**：封裝業務邏輯的單一職責類別
- **Repository Interface**：定義資料存取的抽象介面
- **Domain Models**：業務領域的資料模型

#### 3. Data Layer
- **Repository Implementation**：實作資料存取邏輯
- **API Service**：處理網路請求
- **Database**：本地資料儲存

## 🔧 技術細節

### 依賴注入 (Hilt)

使用 Hilt 進行依賴注入，主要模組包括：

```kotlin
// NetworkModule - 網路相關依賴
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideRetrofit(): Retrofit { ... }
}

// DatabaseModule - 資料庫相關依賴
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    @Provides
    @Singleton
    fun provideDatabase(): VegetableDatabase { ... }
}

// RepositoryModule - Repository 綁定
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    @Binds
    abstract fun bindRepository(): VegetableRepository
}
```

### 狀態管理

使用 StateFlow 和 Compose State 進行響應式狀態管理：

```kotlin
class VegetableViewModel @Inject constructor() : ViewModel() {
    private val _uiState = MutableStateFlow(VegetablePriceUiState())
    val uiState: StateFlow<VegetablePriceUiState> = _uiState.asStateFlow()
    
    // 監聽多個狀態變化
    combine(searchQuery, selectedCategory, sortOption) { query, category, sort ->
        Triple(query, category, sort)
    }.onEach { (query, category, sort) ->
        loadVegetables(query, category, sort)
    }.launchIn(viewModelScope)
}
```

### 錯誤處理

統一的錯誤處理機制：

```kotlin
sealed class ApiResult<out T> {
    data class Success<out T>(val data: T) : ApiResult<T>()
    data class Error(val exception: Throwable) : ApiResult<Nothing>()
    data object Loading : ApiResult<Nothing>()
}

// 在 Repository 中處理錯誤
override suspend fun refreshVegetables(): ApiResult<List<VegetablePrice>> {
    return try {
        RetryUtils.retryWithExponentialBackoff {
            // API 呼叫邏輯
        }
    } catch (e: Exception) {
        ApiResult.Error(handleNetworkError(e))
    }
}
```

## 📱 UI 組件設計

### 設計原則
1. **可重用性**：組件設計為可在多處使用
2. **可組合性**：小組件組合成大組件
3. **狀態提升**：狀態管理在適當的層級
4. **無副作用**：Composable 函數保持純淨

### 主要組件

#### VegetableCard
```kotlin
@Composable
fun VegetableCard(
    vegetable: VegetablePrice,
    onFavoriteClick: (String, Boolean) -> Unit,
    onCardClick: (VegetablePrice) -> Unit,
    modifier: Modifier = Modifier
) {
    // 卡片實作
}
```

#### SearchAndFilterBar
```kotlin
@Composable
fun SearchAndFilterBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    selectedCategory: String,
    categories: List<String>,
    onCategoryChange: (String) -> Unit,
    // ... 其他參數
) {
    // 搜尋和篩選實作
}
```

## 🗄️ 資料管理

### Room Database

```kotlin
@Entity(tableName = "vegetable_prices")
data class VegetablePrice(
    @PrimaryKey val id: String,
    @SerializedName("交易日期") val 交易日期: String,
    @SerializedName("作物名稱") val 作物名稱: String,
    // ... 其他欄位
)

@Dao
interface VegetableDao {
    @Query("SELECT * FROM vegetable_prices ORDER BY 交易日期 DESC")
    fun getAllVegetables(): Flow<List<VegetablePrice>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVegetables(vegetables: List<VegetablePrice>)
}
```

### API 服務

```kotlin
interface VegetableApiService {
    @GET("Service/OpenData/FromM/FarmTransData.aspx")
    suspend fun getVegetablePrices(): Response<List<VegetablePrice>>
}
```

## 🧪 測試策略

### 測試金字塔
1. **單元測試**：測試個別類別和函數
2. **整合測試**：測試組件間的互動
3. **UI 測試**：測試使用者介面

### 測試工具
- **JUnit**：單元測試框架
- **MockK**：Kotlin 模擬框架
- **Compose Testing**：UI 測試
- **Coroutines Test**：協程測試

### 測試範例

```kotlin
@Test
fun `refreshVegetables should return success when api call succeeds`() = runTest {
    // Given
    val response = mockk<Response<List<VegetablePrice>>>()
    every { response.isSuccessful } returns true
    every { response.body() } returns sampleVegetables
    
    // When
    val result = repository.refreshVegetables()
    
    // Then
    assertTrue(result is ApiResult.Success)
}
```

## 🚀 建置和部署

### Gradle 設定

```kotlin
android {
    compileSdk = 35
    
    defaultConfig {
        minSdk = 24
        targetSdk = 35
    }
    
    buildFeatures {
        compose = true
    }
}

dependencies {
    // Compose BOM
    implementation(platform("androidx.compose:compose-bom:2024.09.00"))
    
    // Hilt
    implementation("com.google.dagger:hilt-android:2.52")
    kapt("com.google.dagger:hilt-compiler:2.52")
    
    // 其他依賴...
}
```

### 建置指令

```bash
# 清理專案
./gradlew clean

# 建置 Debug 版本
./gradlew assembleDebug

# 建置 Release 版本
./gradlew assembleRelease

# 執行測試
./gradlew test

# 執行 Lint 檢查
./gradlew lint
```

## 📊 效能優化

### 記憶體優化
1. 使用 `LazyColumn` 進行列表虛擬化
2. 適當的圖片快取和壓縮
3. 避免記憶體洩漏

### 網路優化
1. 實作重試機制
2. 使用本地快取減少網路請求
3. 壓縮網路請求資料

### UI 效能
1. 避免不必要的重組
2. 使用 `remember` 快取計算結果
3. 適當的狀態提升

## 🔄 持續整合

### GitHub Actions 設定

```yaml
name: Android CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    - name: Run tests
      run: ./gradlew test
```

## 📝 程式碼規範

### Kotlin 編碼風格
- 遵循 [Kotlin 官方編碼慣例](https://kotlinlang.org/docs/coding-conventions.html)
- 使用 4 個空格縮排
- 類別和函數使用 KDoc 註解

### Compose 最佳實踐
- Composable 函數使用 PascalCase
- 參數按照：必需參數、可選參數、Modifier、回調函數的順序
- 使用 Preview 註解提供預覽

### Git 提交規範
```
feat: 新增功能
fix: 修復錯誤
docs: 文件更新
style: 程式碼格式調整
refactor: 重構
test: 測試相關
chore: 建置或輔助工具變動
```

## 🔮 未來規劃

### 短期目標
- [ ] 加入圖表顯示價格趨勢
- [ ] 實作推播通知功能
- [ ] 支援多語言介面

### 長期目標
- [ ] 加入價格預測功能
- [ ] 整合更多資料來源
- [ ] 開發 iOS 版本

---

這份開發文件會隨著專案演進持續更新。如有任何技術問題，歡迎提出討論！
