package com.oojohn.vege4.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 網路狀態監控工具
 */
@Singleton
class NetworkMonitor @Inject constructor(
    private val context: Context
) {
    
    /**
     * 監控網路連線狀態
     */
    val isConnected: Flow<Boolean> = callbackFlow {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                trySend(true)
            }
            
            override fun onLost(network: Network) {
                trySend(false)
            }
            
            override fun onCapabilitiesChanged(
                network: Network,
                networkCapabilities: NetworkCapabilities
            ) {
                val connected = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                trySend(connected)
            }
        }
        
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, callback)
        
        // 發送初始狀態
        trySend(isCurrentlyConnected())
        
        awaitClose {
            connectivityManager.unregisterNetworkCallback(callback)
        }
    }.distinctUntilChanged()
    
    /**
     * 檢查當前網路連線狀態
     */
    fun isCurrentlyConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
}

/**
 * 重試機制工具
 */
object RetryUtils {
    
    /**
     * 指數退避重試
     */
    suspend fun <T> retryWithExponentialBackoff(
        maxRetries: Int = 3,
        initialDelayMs: Long = 1000,
        maxDelayMs: Long = 10000,
        factor: Double = 2.0,
        block: suspend () -> T
    ): T {
        var currentDelay = initialDelayMs
        repeat(maxRetries) { attempt ->
            try {
                return block()
            } catch (e: Exception) {
                if (attempt == maxRetries - 1) {
                    throw e
                }
                
                kotlinx.coroutines.delay(currentDelay)
                currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelayMs)
            }
        }
        throw IllegalStateException("Should not reach here")
    }
}

/**
 * 快取管理工具
 */
object CacheUtils {
    
    /**
     * 檢查資料是否過期
     */
    fun isDataExpired(lastUpdateTime: Long, cacheValidityMs: Long = 30 * 60 * 1000): Boolean {
        return System.currentTimeMillis() - lastUpdateTime > cacheValidityMs
    }
    
    /**
     * 格式化快取時間
     */
    fun formatCacheTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "剛剛更新"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)} 分鐘前更新"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)} 小時前更新"
            else -> "${diff / (24 * 60 * 60 * 1000)} 天前更新"
        }
    }
}
