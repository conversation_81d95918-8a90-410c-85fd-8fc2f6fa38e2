# API 參考文件

## 📡 資料來源

Vege4 使用行政院農業委員會提供的開放資料 API。

### 基本資訊
- **API 提供者**：行政院農業委員會
- **資料集名稱**：農產品交易行情
- **基礎 URL**：`https://data.coa.gov.tw/`
- **資料格式**：JSON
- **更新頻率**：每日更新
- **授權方式**：政府資料開放授權條款

## 🔗 API 端點

### 取得農產品交易行情

```
GET /Service/OpenData/FromM/FarmTransData.aspx
```

#### 描述
取得最新的農產品批發市場交易行情資料。

#### 請求參數
此 API 不需要額外參數，會回傳所有可用的交易資料。

#### 回應格式

```json
[
  {
    "交易日期": "113/01/15",
    "作物代號": "FN01",
    "作物名稱": "白蘿蔔",
    "市場代號": "101",
    "市場名稱": "台北果菜市場",
    "種類代碼": "N04",
    "上價": "25.0",
    "中價": "20.0",
    "下價": "15.0",
    "平均價": "20.0",
    "交易量": "1500.0"
  }
]
```

#### 欄位說明

| 欄位名稱 | 類型 | 說明 | 範例 |
|---------|------|------|------|
| 交易日期 | String | 交易日期（民國年/月/日） | "113/01/15" |
| 作物代號 | String | 作物的唯一識別代號 | "FN01" |
| 作物名稱 | String | 作物的中文名稱 | "白蘿蔔" |
| 市場代號 | String | 批發市場的代號 | "101" |
| 市場名稱 | String | 批發市場的名稱 | "台北果菜市場" |
| 種類代碼 | String | 作物種類分類代碼 | "N04" |
| 上價 | String | 最高交易價格（元/公斤） | "25.0" |
| 中價 | String | 中間交易價格（元/公斤） | "20.0" |
| 下價 | String | 最低交易價格（元/公斤） | "15.0" |
| 平均價 | String | 平均交易價格（元/公斤） | "20.0" |
| 交易量 | String | 交易數量（公斤） | "1500.0" |

## 📊 資料說明

### 種類代碼對照表

| 代碼 | 分類名稱 | 說明 |
|------|----------|------|
| N04 | 根莖類 | 白蘿蔔、紅蘿蔔、馬鈴薯等 |
| N05 | 葉菜類 | 高麗菜、菠菜、青江菜等 |
| N06 | 花菜類 | 花椰菜、青花菜等 |
| N07 | 果菜類 | 番茄、茄子、青椒等 |
| N08 | 豆菜類 | 豌豆、四季豆等 |
| N09 | 瓜菜類 | 小黃瓜、絲瓜、冬瓜等 |

### 價格說明

- **上價**：當日該作物的最高成交價格
- **中價**：當日該作物的中位數成交價格
- **下價**：當日該作物的最低成交價格
- **平均價**：當日該作物的平均成交價格
- **單位**：新台幣元/公斤

### 交易量說明

- **單位**：公斤
- **說明**：當日該作物在該市場的總交易數量

## 🔧 在 Vege4 中的實作

### API 服務介面

```kotlin
interface VegetableApiService {
    @GET("Service/OpenData/FromM/FarmTransData.aspx")
    suspend fun getVegetablePrices(): Response<List<VegetablePrice>>
    
    companion object {
        const val BASE_URL = "https://data.coa.gov.tw/"
    }
}
```

### 資料模型

```kotlin
@Entity(tableName = "vegetable_prices")
data class VegetablePrice(
    @PrimaryKey
    val id: String = "${交易日期}_${作物代號}_${種類代碼}",
    
    @SerializedName("交易日期")
    val 交易日期: String,
    
    @SerializedName("種類代碼")
    val 種類代碼: String,
    
    @SerializedName("作物代號")
    val 作物代號: String,
    
    @SerializedName("作物名稱")
    val 作物名稱: String,
    
    @SerializedName("市場代號")
    val 市場代號: String? = null,
    
    @SerializedName("市場名稱")
    val 市場名稱: String? = null,
    
    @SerializedName("上價")
    val 上價: String? = null,
    
    @SerializedName("中價")
    val 中價: String? = null,
    
    @SerializedName("下價")
    val 下價: String? = null,
    
    @SerializedName("平均價")
    val 平均價: String? = null,
    
    @SerializedName("交易量")
    val 交易量: String? = null,
    
    // 本地欄位
    val isFavorite: Boolean = false,
    val lastUpdated: Long = System.currentTimeMillis()
)
```

### 網路設定

```kotlin
@Provides
@Singleton
fun provideOkHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .addInterceptor(HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        })
        .build()
}
```

## ⚠️ 注意事項

### 資料限制

1. **更新頻率**：資料通常每日更新，但可能因假日或系統維護延遲
2. **資料完整性**：某些欄位可能為空值或 "-"，需要適當處理
3. **日期格式**：使用民國年格式，需要轉換為西元年

### 錯誤處理

```kotlin
private fun handleNetworkError(exception: Exception): NetworkError {
    return when (exception) {
        is SocketTimeoutException -> NetworkError.Timeout
        is IOException -> NetworkError.NetworkUnavailable
        is HttpException -> NetworkError.ServerError(
            exception.code(),
            exception.message()
        )
        else -> NetworkError.UnknownError(
            exception.message ?: "Unknown error occurred"
        )
    }
}
```

### 重試機制

```kotlin
suspend fun <T> retryWithExponentialBackoff(
    maxRetries: Int = 3,
    initialDelayMs: Long = 1000,
    maxDelayMs: Long = 10000,
    factor: Double = 2.0,
    block: suspend () -> T
): T {
    var currentDelay = initialDelayMs
    repeat(maxRetries) { attempt ->
        try {
            return block()
        } catch (e: Exception) {
            if (attempt == maxRetries - 1) {
                throw e
            }
            delay(currentDelay)
            currentDelay = (currentDelay * factor).toLong().coerceAtMost(maxDelayMs)
        }
    }
    throw IllegalStateException("Should not reach here")
}
```

## 📈 使用統計

### 快取策略

- **快取時間**：30 分鐘
- **快取位置**：本地 Room 資料庫
- **快取更新**：手動重新整理或自動檢查

### 效能考量

- **資料大小**：每次請求約 100-500KB
- **回應時間**：通常 2-5 秒
- **建議**：在 Wi-Fi 環境下使用以節省行動數據

## 🔗 相關連結

- [政府資料開放平臺](https://data.gov.tw/)
- [農業委員會開放資料](https://data.coa.gov.tw/)
- [政府資料開放授權條款](https://data.gov.tw/license)

---

此 API 文件會隨著資料來源的變更而更新。如發現資料異常或 API 變更，請及時回報。
