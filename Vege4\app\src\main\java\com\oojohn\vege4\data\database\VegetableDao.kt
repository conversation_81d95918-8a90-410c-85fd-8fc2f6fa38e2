package com.oojohn.vege4.data.database

import androidx.room.*
import com.oojohn.vege4.data.model.VegetablePrice
import kotlinx.coroutines.flow.Flow

/**
 * 菜價資料存取物件 (DAO)
 */
@Dao
interface VegetableDao {
    
    /**
     * 取得所有菜價資料 (Flow 形式，支援響應式更新)
     */
    @Query("SELECT * FROM vegetable_prices ORDER BY 交易日期 DESC")
    fun getAllVegetables(): Flow<List<VegetablePrice>>
    
    /**
     * 根據日期範圍取得菜價資料
     */
    @Query("SELECT * FROM vegetable_prices WHERE 交易日期 BETWEEN :startDate AND :endDate ORDER BY 交易日期 DESC")
    fun getVegetablesByDateRange(startDate: String, endDate: String): Flow<List<VegetablePrice>>
    
    /**
     * 根據作物名稱搜尋
     */
    @Query("SELECT * FROM vegetable_prices WHERE 作物名稱 LIKE '%' || :name || '%' ORDER BY 交易日期 DESC")
    fun searchVegetablesByName(name: String): Flow<List<VegetablePrice>>
    
    /**
     * 取得收藏的菜價資料
     */
    @Query("SELECT * FROM vegetable_prices WHERE isFavorite = 1 ORDER BY 交易日期 DESC")
    fun getFavoriteVegetables(): Flow<List<VegetablePrice>>
    
    /**
     * 根據種類代碼篩選
     */
    @Query("SELECT * FROM vegetable_prices WHERE 種類代碼 = :categoryCode ORDER BY 交易日期 DESC")
    fun getVegetablesByCategory(categoryCode: String): Flow<List<VegetablePrice>>
    
    /**
     * 取得所有種類代碼 (用於篩選選項)
     */
    @Query("SELECT DISTINCT 種類代碼 FROM vegetable_prices ORDER BY 種類代碼")
    suspend fun getAllCategories(): List<String>
    
    /**
     * 插入單筆菜價資料
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVegetable(vegetable: VegetablePrice)
    
    /**
     * 插入多筆菜價資料
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVegetables(vegetables: List<VegetablePrice>)
    
    /**
     * 更新菜價資料
     */
    @Update
    suspend fun updateVegetable(vegetable: VegetablePrice)
    
    /**
     * 更新收藏狀態
     */
    @Query("UPDATE vegetable_prices SET isFavorite = :isFavorite WHERE id = :id")
    suspend fun updateFavoriteStatus(id: String, isFavorite: Boolean)
    
    /**
     * 刪除單筆菜價資料
     */
    @Delete
    suspend fun deleteVegetable(vegetable: VegetablePrice)
    
    /**
     * 刪除所有菜價資料
     */
    @Query("DELETE FROM vegetable_prices")
    suspend fun deleteAllVegetables()
    
    /**
     * 刪除過期資料 (超過指定天數)
     */
    @Query("DELETE FROM vegetable_prices WHERE lastUpdated < :timestamp")
    suspend fun deleteOldData(timestamp: Long)
    
    /**
     * 取得資料總數
     */
    @Query("SELECT COUNT(*) FROM vegetable_prices")
    suspend fun getVegetableCount(): Int
    
    /**
     * 檢查是否有資料
     */
    @Query("SELECT EXISTS(SELECT 1 FROM vegetable_prices LIMIT 1)")
    suspend fun hasData(): Boolean
    
    /**
     * 取得最新更新時間
     */
    @Query("SELECT MAX(lastUpdated) FROM vegetable_prices")
    suspend fun getLastUpdateTime(): Long?
}
