package com.oojohn.vege4.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.presentation.components.VegetableCard
import com.oojohn.vege4.presentation.viewmodel.FavoriteViewModel
import com.oojohn.vege4.ui.theme.Vege4Theme

/**
 * 收藏頁面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoriteScreen(
    onBackClick: () -> Unit,
    onCardClick: (VegetablePrice) -> Unit,
    viewModel: FavoriteViewModel = hiltViewModel(),
    modifier: Modifier = Modifier
) {
    val favoriteVegetables by viewModel.favoriteVegetables.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("我的收藏") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                favoriteVegetables.isEmpty() -> {
                    EmptyFavoriteState(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                else -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(bottom = 16.dp)
                    ) {
                        items(
                            items = favoriteVegetables,
                            key = { it.id }
                        ) { vegetable ->
                            VegetableCard(
                                vegetable = vegetable,
                                onFavoriteClick = { id, isFavorite ->
                                    viewModel.toggleFavorite(id, isFavorite)
                                },
                                onCardClick = onCardClick
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 空收藏狀態組件
 */
@Composable
fun EmptyFavoriteState(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "💝",
            style = MaterialTheme.typography.displayLarge
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "還沒有收藏任何菜價",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "在菜價列表中點擊愛心圖示來收藏您感興趣的菜價資訊",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Preview(showBackground = true)
@Composable
fun FavoriteScreenPreview() {
    Vege4Theme {
        Surface {
            EmptyFavoriteState()
        }
    }
}
