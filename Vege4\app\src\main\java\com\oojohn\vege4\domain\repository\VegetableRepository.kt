package com.oojohn.vege4.domain.repository

import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.model.VegetablePrice
import kotlinx.coroutines.flow.Flow

/**
 * 菜價資料倉庫介面
 * 定義資料存取的抽象方法
 */
interface VegetableRepository {
    
    /**
     * 取得所有菜價資料 (本地 + 遠端)
     */
    fun getAllVegetables(): Flow<List<VegetablePrice>>
    
    /**
     * 從遠端 API 重新整理資料
     */
    suspend fun refreshVegetables(): ApiResult<List<VegetablePrice>>
    
    /**
     * 根據名稱搜尋菜價
     */
    fun searchVegetablesByName(name: String): Flow<List<VegetablePrice>>
    
    /**
     * 根據種類篩選菜價
     */
    fun getVegetablesByCategory(categoryCode: String): Flow<List<VegetablePrice>>
    
    /**
     * 取得收藏的菜價
     */
    fun getFavoriteVegetables(): Flow<List<VegetablePrice>>
    
    /**
     * 更新收藏狀態
     */
    suspend fun updateFavoriteStatus(id: String, isFavorite: Boolean): ApiResult<Unit>
    
    /**
     * 取得所有種類代碼
     */
    suspend fun getAllCategories(): ApiResult<List<String>>
    
    /**
     * 清除快取資料
     */
    suspend fun clearCache(): ApiResult<Unit>
    
    /**
     * 檢查是否有本地資料
     */
    suspend fun hasLocalData(): Boolean
    
    /**
     * 取得最後更新時間
     */
    suspend fun getLastUpdateTime(): Long?
}
