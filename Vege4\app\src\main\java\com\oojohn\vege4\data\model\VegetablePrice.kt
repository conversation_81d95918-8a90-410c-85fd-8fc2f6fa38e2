package com.oojohn.vege4.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 菜價資料模型
 * 對應政府開放資料 API 的回應格式
 */
@Entity(tableName = "vegetable_prices")
data class VegetablePrice(
    @PrimaryKey
    val id: String = "${交易日期}_${作物代號}_${種類代碼}",
    
    @SerializedName("交易日期")
    val 交易日期: String,
    
    @SerializedName("種類代碼")
    val 種類代碼: String,
    
    @SerializedName("作物代號")
    val 作物代號: String,
    
    @SerializedName("作物名稱")
    val 作物名稱: String,
    
    @SerializedName("市場代號")
    val 市場代號: String? = null,
    
    @SerializedName("市場名稱")
    val 市場名稱: String? = null,
    
    @SerializedName("上價")
    val 上價: String? = null,
    
    @SerializedName("中價")
    val 中價: String? = null,
    
    @SerializedName("下價")
    val 下價: String? = null,
    
    @SerializedName("平均價")
    val 平均價: String? = null,
    
    @SerializedName("交易量")
    val 交易量: String? = null,
    
    // 本地欄位
    val isFavorite: Boolean = false,
    val lastUpdated: Long = System.currentTimeMillis()
) {
    /**
     * 取得格式化的價格資訊
     */
    fun getFormattedPrice(): String {
        return when {
            !平均價.isNullOrEmpty() && 平均價 != "-" -> "平均價: $平均價 元"
            !中價.isNullOrEmpty() && 中價 != "-" -> "中價: $中價 元"
            !上價.isNullOrEmpty() && 上價 != "-" -> "上價: $上價 元"
            else -> "價格資訊不詳"
        }
    }
    
    /**
     * 取得格式化的交易量
     */
    fun getFormattedVolume(): String {
        return if (!交易量.isNullOrEmpty() && 交易量 != "-") {
            "交易量: $交易量"
        } else {
            "交易量不詳"
        }
    }
    
    /**
     * 取得格式化的日期
     */
    fun getFormattedDate(): String {
        return try {
            // 假設日期格式為 YYYY/MM/DD 或 YYYY-MM-DD
            val cleanDate = 交易日期.replace("-", "/")
            val parts = cleanDate.split("/")
            if (parts.size == 3) {
                "${parts[0]}年${parts[1]}月${parts[2]}日"
            } else {
                交易日期
            }
        } catch (e: Exception) {
            交易日期
        }
    }
}

/**
 * UI 狀態用的資料類別
 */
data class VegetablePriceUiState(
    val isLoading: Boolean = false,
    val vegetables: List<VegetablePrice> = emptyList(),
    val error: String? = null,
    val searchQuery: String = "",
    val selectedCategory: String = "全部",
    val sortBy: SortOption = SortOption.DATE_DESC
)

/**
 * 排序選項
 */
enum class SortOption(val displayName: String) {
    DATE_DESC("日期 (新到舊)"),
    DATE_ASC("日期 (舊到新)"),
    NAME_ASC("名稱 (A-Z)"),
    NAME_DESC("名稱 (Z-A)"),
    PRICE_ASC("價格 (低到高)"),
    PRICE_DESC("價格 (高到低)")
}

/**
 * API 回應包裝類別
 */
data class ApiResponse<T>(
    val success: Boolean,
    val data: T?,
    val message: String?
)
