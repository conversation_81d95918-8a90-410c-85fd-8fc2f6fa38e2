package com.oojohn.vege4.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.model.SortOption
import com.oojohn.vege4.data.model.VegetablePriceUiState
import com.oojohn.vege4.domain.usecase.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 菜價主畫面的 ViewModel
 */
@HiltViewModel
class VegetableViewModel @Inject constructor(
    private val getVegetablesUseCase: GetVegetablesUseCase,
    private val refreshVegetablesUseCase: RefreshVegetablesUseCase,
    private val updateFavoriteStatusUseCase: UpdateFavoriteStatusUseCase,
    private val getCategoriesUseCase: GetCategoriesUseCase
) : ViewModel() {
    
    // UI 狀態
    private val _uiState = MutableStateFlow(VegetablePriceUiState())
    val uiState: StateFlow<VegetablePriceUiState> = _uiState.asStateFlow()
    
    // 搜尋查詢
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // 選擇的種類
    private val _selectedCategory = MutableStateFlow("全部")
    val selectedCategory: StateFlow<String> = _selectedCategory.asStateFlow()
    
    // 排序選項
    private val _sortOption = MutableStateFlow(SortOption.DATE_DESC)
    val sortOption: StateFlow<SortOption> = _sortOption.asStateFlow()
    
    // 可用的種類列表
    private val _categories = MutableStateFlow<List<String>>(emptyList())
    val categories: StateFlow<List<String>> = _categories.asStateFlow()
    
    init {
        // 監聽搜尋、篩選和排序的變化，自動更新菜價列表
        combine(
            _searchQuery,
            _selectedCategory,
            _sortOption
        ) { query, category, sort ->
            Triple(query, category, sort)
        }.onEach { (query, category, sort) ->
            loadVegetables(query, category, sort)
        }.launchIn(viewModelScope)
        
        // 載入種類列表
        loadCategories()
    }
    
    /**
     * 載入菜價資料
     */
    private fun loadVegetables(
        query: String = _searchQuery.value,
        category: String = _selectedCategory.value,
        sort: SortOption = _sortOption.value
    ) {
        viewModelScope.launch {
            getVegetablesUseCase(query, category, sort)
                .catch { exception ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = exception.message ?: "載入資料時發生錯誤"
                    )
                }
                .collect { vegetables ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        vegetables = vegetables,
                        error = null
                    )
                }
        }
    }
    
    /**
     * 重新整理菜價資料
     */
    fun refreshVegetables() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            when (val result = refreshVegetablesUseCase()) {
                is ApiResult.Success -> {
                    // 資料會透過 Flow 自動更新
                    _uiState.value = _uiState.value.copy(isLoading = false)
                }
                is ApiResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.exception.message ?: "重新整理失敗"
                    )
                }
                is ApiResult.Loading -> {
                    // 已經在上面設定 loading 狀態
                }
            }
        }
    }
    
    /**
     * 更新搜尋查詢
     */
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    /**
     * 更新選擇的種類
     */
    fun updateSelectedCategory(category: String) {
        _selectedCategory.value = category
    }
    
    /**
     * 更新排序選項
     */
    fun updateSortOption(option: SortOption) {
        _sortOption.value = option
    }
    
    /**
     * 切換收藏狀態
     */
    fun toggleFavorite(vegetableId: String, currentFavoriteStatus: Boolean) {
        viewModelScope.launch {
            when (val result = updateFavoriteStatusUseCase(vegetableId, !currentFavoriteStatus)) {
                is ApiResult.Success -> {
                    // 成功，資料會透過 Flow 自動更新
                }
                is ApiResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        error = "更新收藏狀態失敗: ${result.exception.message}"
                    )
                }
                is ApiResult.Loading -> {
                    // 不需要特別處理
                }
            }
        }
    }
    
    /**
     * 載入種類列表
     */
    private fun loadCategories() {
        viewModelScope.launch {
            when (val result = getCategoriesUseCase()) {
                is ApiResult.Success -> {
                    _categories.value = result.data
                }
                is ApiResult.Error -> {
                    // 載入種類失敗，使用預設值
                    _categories.value = listOf("全部")
                }
                is ApiResult.Loading -> {
                    // 不需要特別處理
                }
            }
        }
    }
    
    /**
     * 清除錯誤訊息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
