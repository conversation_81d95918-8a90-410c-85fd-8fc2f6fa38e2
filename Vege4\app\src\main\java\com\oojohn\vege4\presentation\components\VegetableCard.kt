package com.oojohn.vege4.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.ui.theme.Vege4Theme

/**
 * 菜價卡片組件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VegetableCard(
    vegetable: VegetablePrice,
    onFavoriteClick: (String, Boolean) -> Unit,
    onCardClick: (VegetablePrice) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = { onCardClick(vegetable) },
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 4.dp),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 標題行：作物名稱和收藏按鈕
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = vegetable.作物名稱,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                IconButton(
                    onClick = { onFavoriteClick(vegetable.id, vegetable.isFavorite) }
                ) {
                    Icon(
                        imageVector = if (vegetable.isFavorite) Icons.Filled.Favorite else Icons.Filled.FavoriteBorder,
                        contentDescription = if (vegetable.isFavorite) "取消收藏" else "加入收藏",
                        tint = if (vegetable.isFavorite) Color.Red else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 價格資訊
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = vegetable.getFormattedPrice(),
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    if (!vegetable.交易量.isNullOrEmpty() && vegetable.交易量 != "-") {
                        Text(
                            text = vegetable.getFormattedVolume(),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 詳細資訊行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // 種類代碼
                Surface(
                    shape = RoundedCornerShape(16.dp),
                    color = MaterialTheme.colorScheme.secondaryContainer
                ) {
                    Text(
                        text = vegetable.種類代碼,
                        style = MaterialTheme.typography.labelMedium,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
                
                // 交易日期
                Text(
                    text = vegetable.getFormattedDate(),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 市場資訊（如果有的話）
            if (!vegetable.市場名稱.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "市場：${vegetable.市場名稱}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun VegetableCardPreview() {
    Vege4Theme {
        VegetableCard(
            vegetable = VegetablePrice(
                交易日期 = "2024/01/15",
                種類代碼 = "N04",
                作物代號 = "FN01",
                作物名稱 = "白蘿蔔",
                市場名稱 = "台北果菜市場",
                上價 = "25",
                中價 = "20",
                下價 = "15",
                平均價 = "20",
                交易量 = "1500",
                isFavorite = false
            ),
            onFavoriteClick = { _, _ -> },
            onCardClick = { }
        )
    }
}
