package com.oojohn.vege4.presentation.viewmodel

import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.model.SortOption
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.domain.usecase.*
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * VegetableViewModel 的單元測試
 */
@OptIn(ExperimentalCoroutinesApi::class)
class VegetableViewModelTest {
    
    private lateinit var viewModel: VegetableViewModel
    private lateinit var getVegetablesUseCase: GetVegetablesUseCase
    private lateinit var refreshVegetablesUseCase: RefreshVegetablesUseCase
    private lateinit var updateFavoriteStatusUseCase: UpdateFavoriteStatusUseCase
    private lateinit var getCategoriesUseCase: GetCategoriesUseCase
    
    private val testDispatcher = StandardTestDispatcher()
    
    private val sampleVegetables = listOf(
        VegetablePrice(
            交易日期 = "2024/01/15",
            種類代碼 = "N04",
            作物代號 = "FN01",
            作物名稱 = "白蘿蔔",
            平均價 = "20"
        ),
        VegetablePrice(
            交易日期 = "2024/01/15",
            種類代碼 = "N05",
            作物代號 = "FN02",
            作物名稱 = "紅蘿蔔",
            平均價 = "25"
        )
    )
    
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        getVegetablesUseCase = mockk()
        refreshVegetablesUseCase = mockk()
        updateFavoriteStatusUseCase = mockk()
        getCategoriesUseCase = mockk()
        
        // 設定預設的 mock 行為
        every { getVegetablesUseCase(any(), any(), any()) } returns flowOf(sampleVegetables)
        coEvery { getCategoriesUseCase() } returns ApiResult.Success(listOf("全部", "N04", "N05"))
        
        viewModel = VegetableViewModel(
            getVegetablesUseCase,
            refreshVegetablesUseCase,
            updateFavoriteStatusUseCase,
            getCategoriesUseCase
        )
    }
    
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        unmockkAll()
    }
    
    @Test
    fun `initial state should be correct`() = runTest {
        // Then
        assertEquals("", viewModel.searchQuery.value)
        assertEquals("全部", viewModel.selectedCategory.value)
        assertEquals(SortOption.DATE_DESC, viewModel.sortOption.value)
        assertFalse(viewModel.uiState.value.isLoading)
    }
    
    @Test
    fun `updateSearchQuery should update search query`() = runTest {
        // Given
        val newQuery = "蘿蔔"
        
        // When
        viewModel.updateSearchQuery(newQuery)
        
        // Then
        assertEquals(newQuery, viewModel.searchQuery.value)
    }
    
    @Test
    fun `updateSelectedCategory should update selected category`() = runTest {
        // Given
        val newCategory = "N04"
        
        // When
        viewModel.updateSelectedCategory(newCategory)
        
        // Then
        assertEquals(newCategory, viewModel.selectedCategory.value)
    }
    
    @Test
    fun `updateSortOption should update sort option`() = runTest {
        // Given
        val newSortOption = SortOption.NAME_ASC
        
        // When
        viewModel.updateSortOption(newSortOption)
        
        // Then
        assertEquals(newSortOption, viewModel.sortOption.value)
    }
    
    @Test
    fun `refreshVegetables should set loading state and call use case`() = runTest {
        // Given
        coEvery { refreshVegetablesUseCase() } returns ApiResult.Success(sampleVegetables)
        
        // When
        viewModel.refreshVegetables()
        
        // 等待協程完成
        advanceUntilIdle()
        
        // Then
        coVerify { refreshVegetablesUseCase() }
        assertFalse(viewModel.uiState.value.isLoading)
    }
    
    @Test
    fun `refreshVegetables should handle error correctly`() = runTest {
        // Given
        val errorMessage = "Network error"
        coEvery { refreshVegetablesUseCase() } returns ApiResult.Error(Exception(errorMessage))
        
        // When
        viewModel.refreshVegetables()
        
        // 等待協程完成
        advanceUntilIdle()
        
        // Then
        assertFalse(viewModel.uiState.value.isLoading)
        assertEquals(errorMessage, viewModel.uiState.value.error)
    }
    
    @Test
    fun `toggleFavorite should call update favorite use case`() = runTest {
        // Given
        val vegetableId = "test_id"
        val currentStatus = false
        coEvery { updateFavoriteStatusUseCase(vegetableId, !currentStatus) } returns ApiResult.Success(Unit)
        
        // When
        viewModel.toggleFavorite(vegetableId, currentStatus)
        
        // 等待協程完成
        advanceUntilIdle()
        
        // Then
        coVerify { updateFavoriteStatusUseCase(vegetableId, !currentStatus) }
    }
    
    @Test
    fun `toggleFavorite should handle error correctly`() = runTest {
        // Given
        val vegetableId = "test_id"
        val currentStatus = false
        val errorMessage = "Update failed"
        coEvery { updateFavoriteStatusUseCase(vegetableId, !currentStatus) } returns ApiResult.Error(Exception(errorMessage))
        
        // When
        viewModel.toggleFavorite(vegetableId, currentStatus)
        
        // 等待協程完成
        advanceUntilIdle()
        
        // Then
        assertTrue(viewModel.uiState.value.error?.contains("更新收藏狀態失敗") == true)
    }
    
    @Test
    fun `clearError should clear error state`() = runTest {
        // Given - 先設定錯誤狀態
        coEvery { refreshVegetablesUseCase() } returns ApiResult.Error(Exception("Test error"))
        viewModel.refreshVegetables()
        advanceUntilIdle()
        
        // When
        viewModel.clearError()
        
        // Then
        assertEquals(null, viewModel.uiState.value.error)
    }
    
    @Test
    fun `categories should be loaded on init`() = runTest {
        // Given - categories use case 已在 setup 中設定
        
        // When - ViewModel 初始化時會自動載入
        advanceUntilIdle()
        
        // Then
        assertEquals(listOf("全部", "N04", "N05"), viewModel.categories.value)
        coVerify { getCategoriesUseCase() }
    }
}
