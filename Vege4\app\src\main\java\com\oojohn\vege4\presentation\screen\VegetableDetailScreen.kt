package com.oojohn.vege4.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.ui.theme.Vege4Theme

/**
 * 菜價詳細資訊畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VegetableDetailScreen(
    vegetable: VegetablePrice,
    onBackClick: () -> Unit,
    onFavoriteClick: (String, Boolean) -> Unit,
    onShareClick: (VegetablePrice) -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(vegetable.作物名稱) },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(
                        onClick = { onFavoriteClick(vegetable.id, vegetable.isFavorite) }
                    ) {
                        Icon(
                            imageVector = if (vegetable.isFavorite) Icons.Filled.Favorite else Icons.Filled.FavoriteBorder,
                            contentDescription = if (vegetable.isFavorite) "取消收藏" else "加入收藏",
                            tint = if (vegetable.isFavorite) Color.Red else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    IconButton(
                        onClick = { onShareClick(vegetable) }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Share,
                            contentDescription = "分享"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 主要資訊卡片
            MainInfoCard(vegetable = vegetable)
            
            // 價格詳細資訊
            PriceDetailCard(vegetable = vegetable)
            
            // 市場資訊
            if (!vegetable.市場名稱.isNullOrEmpty()) {
                MarketInfoCard(vegetable = vegetable)
            }
            
            // 其他資訊
            AdditionalInfoCard(vegetable = vegetable)
        }
    }
}

/**
 * 主要資訊卡片
 */
@Composable
fun MainInfoCard(
    vegetable: VegetablePrice,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = vegetable.作物名稱,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Surface(
                    shape = RoundedCornerShape(20.dp),
                    color = MaterialTheme.colorScheme.primaryContainer
                ) {
                    Text(
                        text = vegetable.種類代碼,
                        style = MaterialTheme.typography.labelLarge,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                
                Text(
                    text = vegetable.getFormattedDate(),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 價格詳細資訊卡片
 */
@Composable
fun PriceDetailCard(
    vegetable: VegetablePrice,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "價格資訊",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 平均價（主要顯示）
            if (!vegetable.平均價.isNullOrEmpty() && vegetable.平均價 != "-") {
                PriceItem(
                    label = "平均價",
                    price = vegetable.平均價,
                    isMain = true
                )
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // 其他價格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                if (!vegetable.上價.isNullOrEmpty() && vegetable.上價 != "-") {
                    PriceItem(
                        label = "上價",
                        price = vegetable.上價,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                if (!vegetable.中價.isNullOrEmpty() && vegetable.中價 != "-") {
                    PriceItem(
                        label = "中價",
                        price = vegetable.中價,
                        modifier = Modifier.weight(1f)
                    )
                }
                
                if (!vegetable.下價.isNullOrEmpty() && vegetable.下價 != "-") {
                    PriceItem(
                        label = "下價",
                        price = vegetable.下價,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            // 交易量
            if (!vegetable.交易量.isNullOrEmpty() && vegetable.交易量 != "-") {
                Spacer(modifier = Modifier.height(16.dp))
                Divider()
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "交易量",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "${vegetable.交易量} 公斤",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

/**
 * 價格項目組件
 */
@Composable
fun PriceItem(
    label: String,
    price: String,
    modifier: Modifier = Modifier,
    isMain: Boolean = false
) {
    Column(
        modifier = modifier,
        horizontalAlignment = if (isMain) Alignment.Start else Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = if (isMain) MaterialTheme.typography.titleMedium else MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = "$price 元",
            style = if (isMain) MaterialTheme.typography.headlineSmall else MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
    }
}

/**
 * 市場資訊卡片
 */
@Composable
fun MarketInfoCard(
    vegetable: VegetablePrice,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "市場資訊",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            InfoRow(
                label = "市場名稱",
                value = vegetable.市場名稱 ?: "未提供"
            )
            
            if (!vegetable.市場代號.isNullOrEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                InfoRow(
                    label = "市場代號",
                    value = vegetable.市場代號
                )
            }
        }
    }
}

/**
 * 其他資訊卡片
 */
@Composable
fun AdditionalInfoCard(
    vegetable: VegetablePrice,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "其他資訊",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            InfoRow(
                label = "作物代號",
                value = vegetable.作物代號
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            InfoRow(
                label = "種類代碼",
                value = vegetable.種類代碼
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            InfoRow(
                label = "交易日期",
                value = vegetable.交易日期
            )
        }
    }
}

/**
 * 資訊行組件
 */
@Composable
fun InfoRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Preview(showBackground = true)
@Composable
fun VegetableDetailScreenPreview() {
    Vege4Theme {
        VegetableDetailScreen(
            vegetable = VegetablePrice(
                交易日期 = "2024/01/15",
                種類代碼 = "N04",
                作物代號 = "FN01",
                作物名稱 = "白蘿蔔",
                市場名稱 = "台北果菜市場",
                市場代號 = "101",
                上價 = "25",
                中價 = "20",
                下價 = "15",
                平均價 = "20",
                交易量 = "1500",
                isFavorite = true
            ),
            onBackClick = {},
            onFavoriteClick = { _, _ -> },
            onShareClick = {}
        )
    }
}
