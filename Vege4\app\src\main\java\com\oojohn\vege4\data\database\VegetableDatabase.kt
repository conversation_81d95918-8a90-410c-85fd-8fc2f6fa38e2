package com.oojohn.vege4.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.oojohn.vege4.data.model.VegetablePrice

/**
 * 菜價資料庫
 */
@Database(
    entities = [VegetablePrice::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class VegetableDatabase : RoomDatabase() {
    
    abstract fun vegetableDao(): VegetableDao
    
    companion object {
        @Volatile
        private var INSTANCE: VegetableDatabase? = null
        
        private const val DATABASE_NAME = "vegetable_database"
        
        fun getDatabase(context: Context): VegetableDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    VegetableDatabase::class.java,
                    DATABASE_NAME
                )
                    .fallbackToDestructiveMigration() // 開發階段使用，正式版本應該提供適當的遷移策略
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * Room 類型轉換器
 */
class Converters {
    // 目前不需要特殊的類型轉換，但保留以備未來使用
}
