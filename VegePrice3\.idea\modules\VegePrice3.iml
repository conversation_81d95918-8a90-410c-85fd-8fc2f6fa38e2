<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id="VegePrice3" external.linked.project.path="$MODULE_DIR$/../.." external.root.project.path="$MODULE_DIR$/../.." external.system.id="GRADLE" external.system.module.group="" external.system.module.version="unspecified" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="java-gradle" name="Java-Gradle">
      <configuration>
        <option name="BUILDABLE" value="false" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager">
    <exclude-output />
    <content url="file://$MODULE_DIR$/../..">
      <excludeFolder url="file://$MODULE_DIR$/../../.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/../../build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>