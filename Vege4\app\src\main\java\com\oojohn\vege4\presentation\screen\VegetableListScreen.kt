package com.oojohn.vege4.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.oojohn.vege4.data.model.VegetablePrice
import com.oojohn.vege4.presentation.components.*
import com.oojohn.vege4.presentation.viewmodel.VegetableViewModel
import com.oojohn.vege4.ui.theme.Vege4Theme
import com.oojohn.vege4.utils.NetworkMonitor
import javax.inject.Inject

/**
 * 菜價列表主畫面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VegetableListScreen(
    viewModel: VegetableViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    val selectedCategory by viewModel.selectedCategory.collectAsStateWithLifecycle()
    val sortOption by viewModel.sortOption.collectAsStateWithLifecycle()
    val categories by viewModel.categories.collectAsStateWithLifecycle()
    
    // 錯誤訊息 Snackbar
    val snackbarHostState = remember { SnackbarHostState() }
    
    // 顯示錯誤訊息
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(
                message = error,
                duration = SnackbarDuration.Short
            )
            viewModel.clearError()
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("菜價查詢") },
                actions = {
                    IconButton(
                        onClick = { viewModel.refreshVegetables() }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "重新整理"
                        )
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜尋和篩選工具列
            SearchAndFilterBar(
                searchQuery = searchQuery,
                onSearchQueryChange = viewModel::updateSearchQuery,
                selectedCategory = selectedCategory,
                categories = categories,
                onCategoryChange = viewModel::updateSelectedCategory,
                sortOption = sortOption,
                onSortOptionChange = viewModel::updateSortOption
            )
            
            // 內容區域
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                when {
                    uiState.isLoading -> {
                        // 載入中指示器
                        LoadingIndicator(
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                    
                    uiState.vegetables.isEmpty() -> {
                        // 空狀態
                        EmptyState(
                            modifier = Modifier.align(Alignment.Center),
                            onRefreshClick = { viewModel.refreshVegetables() }
                        )
                    }
                    
                    else -> {
                        // 菜價列表
                        VegetableList(
                            vegetables = uiState.vegetables,
                            onFavoriteClick = viewModel::toggleFavorite,
                            onCardClick = { /* TODO: 導航到詳細頁面 */ }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 菜價列表組件
 */
@Composable
fun VegetableList(
    vegetables: List<VegetablePrice>,
    onFavoriteClick: (String, Boolean) -> Unit,
    onCardClick: (VegetablePrice) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(bottom = 16.dp)
    ) {
        items(
            items = vegetables,
            key = { it.id }
        ) { vegetable ->
            VegetableCard(
                vegetable = vegetable,
                onFavoriteClick = onFavoriteClick,
                onCardClick = onCardClick
            )
        }
    }
}

/**
 * 載入中指示器
 */
@Composable
fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator()
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "載入中...",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

/**
 * 空狀態組件
 */
@Composable
fun EmptyState(
    modifier: Modifier = Modifier,
    onRefreshClick: () -> Unit
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "📱",
            style = MaterialTheme.typography.displayLarge
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "目前沒有菜價資料",
            style = MaterialTheme.typography.headlineSmall,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "點擊下方按鈕重新載入資料",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(24.dp))
        Button(
            onClick = onRefreshClick
        ) {
            Icon(
                imageVector = Icons.Default.Refresh,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("重新載入")
        }
    }
}

@Preview(showBackground = true)
@Composable
fun VegetableListScreenPreview() {
    Vege4Theme {
        // Preview 無法使用 Hilt，所以這裡只顯示基本結構
        Surface {
            Text(
                text = "菜價查詢應用程式",
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}
