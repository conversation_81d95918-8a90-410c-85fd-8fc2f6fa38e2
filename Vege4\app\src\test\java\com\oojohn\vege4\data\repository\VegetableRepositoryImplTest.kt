package com.oojohn.vege4.data.repository

import com.oojohn.vege4.data.api.ApiResult
import com.oojohn.vege4.data.api.NetworkError
import com.oojohn.vege4.data.api.VegetableApiService
import com.oojohn.vege4.data.database.VegetableDao
import com.oojohn.vege4.data.model.VegetablePrice
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import retrofit2.Response
import java.io.IOException
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * VegetableRepositoryImpl 的單元測試
 */
class VegetableRepositoryImplTest {
    
    private lateinit var repository: VegetableRepositoryImpl
    private lateinit var apiService: VegetableApiService
    private lateinit var dao: VegetableDao
    
    private val sampleVegetables = listOf(
        VegetablePrice(
            交易日期 = "2024/01/15",
            種類代碼 = "N04",
            作物代號 = "FN01",
            作物名稱 = "白蘿蔔",
            平均價 = "20"
        ),
        VegetablePrice(
            交易日期 = "2024/01/15",
            種類代碼 = "N05",
            作物代號 = "FN02",
            作物名稱 = "紅蘿蔔",
            平均價 = "25"
        )
    )
    
    @Before
    fun setup() {
        apiService = mockk()
        dao = mockk()
        repository = VegetableRepositoryImpl(apiService, dao)
    }
    
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    @Test
    fun `getAllVegetables should return flow from dao`() = runTest {
        // Given
        every { dao.getAllVegetables() } returns flowOf(sampleVegetables)
        
        // When
        val result = repository.getAllVegetables()
        
        // Then
        result.collect { vegetables ->
            assertEquals(sampleVegetables, vegetables)
        }
        verify { dao.getAllVegetables() }
    }
    
    @Test
    fun `refreshVegetables should return success when api call succeeds`() = runTest {
        // Given
        val response = mockk<Response<List<VegetablePrice>>>()
        every { response.isSuccessful } returns true
        every { response.body() } returns sampleVegetables
        
        coEvery { apiService.getVegetablePrices() } returns response
        coEvery { dao.deleteAllVegetables() } just Runs
        coEvery { dao.insertVegetables(any()) } just Runs
        
        // When
        val result = repository.refreshVegetables()
        
        // Then
        assertTrue(result is ApiResult.Success)
        assertEquals(sampleVegetables, (result as ApiResult.Success).data)
        
        coVerify { dao.deleteAllVegetables() }
        coVerify { dao.insertVegetables(any()) }
    }
    
    @Test
    fun `refreshVegetables should return error when api call fails`() = runTest {
        // Given
        val response = mockk<Response<List<VegetablePrice>>>()
        every { response.isSuccessful } returns false
        every { response.code() } returns 500
        every { response.message() } returns "Internal Server Error"
        
        coEvery { apiService.getVegetablePrices() } returns response
        
        // When
        val result = repository.refreshVegetables()
        
        // Then
        assertTrue(result is ApiResult.Error)
        assertTrue((result as ApiResult.Error).exception is NetworkError.ServerError)
    }
    
    @Test
    fun `refreshVegetables should return error when network exception occurs`() = runTest {
        // Given
        coEvery { apiService.getVegetablePrices() } throws IOException("Network error")
        
        // When
        val result = repository.refreshVegetables()
        
        // Then
        assertTrue(result is ApiResult.Error)
        assertTrue((result as ApiResult.Error).exception is NetworkError.NetworkUnavailable)
    }
    
    @Test
    fun `searchVegetablesByName should return all vegetables when query is blank`() = runTest {
        // Given
        every { dao.getAllVegetables() } returns flowOf(sampleVegetables)
        
        // When
        val result = repository.searchVegetablesByName("")
        
        // Then
        result.collect { vegetables ->
            assertEquals(sampleVegetables, vegetables)
        }
        verify { dao.getAllVegetables() }
    }
    
    @Test
    fun `searchVegetablesByName should call dao search when query is not blank`() = runTest {
        // Given
        val query = "蘿蔔"
        val filteredVegetables = listOf(sampleVegetables[0])
        every { dao.searchVegetablesByName(query) } returns flowOf(filteredVegetables)
        
        // When
        val result = repository.searchVegetablesByName(query)
        
        // Then
        result.collect { vegetables ->
            assertEquals(filteredVegetables, vegetables)
        }
        verify { dao.searchVegetablesByName(query) }
    }
    
    @Test
    fun `updateFavoriteStatus should return success when dao operation succeeds`() = runTest {
        // Given
        val vegetableId = "test_id"
        val isFavorite = true
        coEvery { dao.updateFavoriteStatus(vegetableId, isFavorite) } just Runs
        
        // When
        val result = repository.updateFavoriteStatus(vegetableId, isFavorite)
        
        // Then
        assertTrue(result is ApiResult.Success)
        coVerify { dao.updateFavoriteStatus(vegetableId, isFavorite) }
    }
    
    @Test
    fun `updateFavoriteStatus should return error when dao operation fails`() = runTest {
        // Given
        val vegetableId = "test_id"
        val isFavorite = true
        coEvery { dao.updateFavoriteStatus(vegetableId, isFavorite) } throws Exception("Database error")
        
        // When
        val result = repository.updateFavoriteStatus(vegetableId, isFavorite)
        
        // Then
        assertTrue(result is ApiResult.Error)
    }
    
    @Test
    fun `hasLocalData should return true when dao has data`() = runTest {
        // Given
        coEvery { dao.hasData() } returns true
        
        // When
        val result = repository.hasLocalData()
        
        // Then
        assertTrue(result)
        coVerify { dao.hasData() }
    }
    
    @Test
    fun `hasLocalData should return false when dao throws exception`() = runTest {
        // Given
        coEvery { dao.hasData() } throws Exception("Database error")
        
        // When
        val result = repository.hasLocalData()
        
        // Then
        assertTrue(!result)
    }
}
